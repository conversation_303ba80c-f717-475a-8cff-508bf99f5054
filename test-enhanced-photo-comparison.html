<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Photo Comparison & ID Data Recovery</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .upload-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .upload-box {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .upload-box:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-box.has-image {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .analysis-section {
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            text-align: center;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .similarity-score {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .score-excellent { color: #28a745; }
        .score-good { color: #17a2b8; }
        .score-fair { color: #ffc107; }
        .score-poor { color: #dc3545; }
        .data-extraction {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .extracted-data {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .data-field {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .analyze-btn {
            background-color: #28a745;
            font-size: 18px;
            padding: 15px 30px;
            margin: 20px 0;
        }
        .analyze-btn:hover {
            background-color: #218838;
        }
        .results-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            display: none;
        }
        .results-section.show {
            display: block;
        }
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #6c757d;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .server-test {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Enhanced Photo Comparison & ID Data Recovery</h1>
        <p>This system demonstrates advanced photo comparison and ID data extraction with real image analysis.</p>

        <div class="server-test">
            <h3>🌐 Server-Based Analysis</h3>
            <p>Test the enhanced verification system with actual server processing:</p>
            <div class="test-buttons">
                <button onclick="testServerVerification()">🔬 Test Server Verification</button>
                <button onclick="openComprehensiveTest()">📋 Open Comprehensive Test</button>
                <button onclick="openMainApp()">🗳️ Open Voting App</button>
            </div>
            <div id="serverResults"></div>
        </div>

        <div class="upload-section">
            <div class="upload-box" id="profileBox">
                <h3>📸 Profile Photo</h3>
                <input type="file" id="profileInput" accept="image/*" style="display: none;">
                <button onclick="document.getElementById('profileInput').click()">Choose Profile Photo</button>
                <div id="profilePreview"></div>
            </div>
            
            <div class="upload-box" id="idBox">
                <h3>🆔 ID Document</h3>
                <input type="file" id="idInput" accept="image/*" style="display: none;">
                <button onclick="document.getElementById('idInput').click()">Choose ID Document</button>
                <div id="idPreview"></div>
            </div>
            
            <div class="upload-box" id="liveBox">
                <h3>📹 Live Photo</h3>
                <input type="file" id="liveInput" accept="image/*" style="display: none;">
                <button onclick="document.getElementById('liveInput').click()">Choose Live Photo</button>
                <div id="livePreview"></div>
            </div>
        </div>

        <button class="analyze-btn" id="analyzeBtn" onclick="performServerAnalysis()" disabled>
            🔬 Analyze with Server (Recommended)
        </button>

        <div class="results-section" id="resultsSection">
            <h2>📊 Analysis Results</h2>
            <div id="analysisContent"></div>
        </div>
    </div>

    <script>
        let profileFile = null;
        let idFile = null;
        let liveFile = null;

        // File input handlers
        document.getElementById('profileInput').addEventListener('change', function(e) {
            profileFile = e.target.files[0];
            showPreview('profilePreview', 'profileBox', profileFile);
            checkAllFilesUploaded();
        });

        document.getElementById('idInput').addEventListener('change', function(e) {
            idFile = e.target.files[0];
            showPreview('idPreview', 'idBox', idFile);
            checkAllFilesUploaded();
        });

        document.getElementById('liveInput').addEventListener('change', function(e) {
            liveFile = e.target.files[0];
            showPreview('livePreview', 'liveBox', liveFile);
            checkAllFilesUploaded();
        });

        function showPreview(previewId, boxId, file) {
            const preview = document.getElementById(previewId);
            const box = document.getElementById(boxId);
            
            if (file) {
                const img = document.createElement('img');
                img.src = URL.createObjectURL(file);
                img.className = 'preview-image';
                img.onload = () => URL.revokeObjectURL(img.src);
                
                preview.innerHTML = '';
                preview.appendChild(img);
                
                const info = document.createElement('div');
                info.innerHTML = `
                    <small>
                        ${file.name}<br>
                        ${(file.size / 1024 / 1024).toFixed(2)} MB
                    </small>
                `;
                preview.appendChild(info);
                
                box.classList.add('has-image');
            }
        }

        function checkAllFilesUploaded() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            analyzeBtn.disabled = !(profileFile && idFile && liveFile);
        }

        async function performServerAnalysis() {
            const resultsSection = document.getElementById('resultsSection');
            const analysisContent = document.getElementById('analysisContent');
            const analyzeBtn = document.getElementById('analyzeBtn');
            
            analyzeBtn.disabled = true;
            analyzeBtn.innerHTML = '🔄 Analyzing...';
            resultsSection.classList.add('show');
            analysisContent.innerHTML = '<div class="loading">🔄 Performing comprehensive server-side analysis...</div>';

            try {
                // Step 1: Verify selfie
                console.log('📸 Step 1: Verifying selfie...');
                const selfieResult = await verifyStep('selfie', { selfie: profileFile });
                
                // Step 2: Verify ID with data extraction
                console.log('🆔 Step 2: Scanning ID and extracting data...');
                const idResult = await verifyStep('id', { selfie: profileFile, idImage: idFile });
                
                // Step 3: Complete verification with live photo
                console.log('📹 Step 3: Complete verification with live photo...');
                const videoResult = await verifyStep('video', { 
                    selfie: profileFile, 
                    idImage: idFile, 
                    videoFrame: liveFile 
                });

                // Display comprehensive results
                displayServerResults(selfieResult, idResult, videoResult);

            } catch (error) {
                console.error('Server analysis failed:', error);
                analysisContent.innerHTML = `<div class="error">❌ Server analysis failed: ${error.message}</div>`;
            } finally {
                analyzeBtn.disabled = false;
                analyzeBtn.innerHTML = '🔬 Analyze with Server (Recommended)';
            }
        }

        async function verifyStep(step, files) {
            const formData = new FormData();
            formData.append('step', step);
            
            Object.keys(files).forEach(key => {
                if (files[key]) {
                    formData.append(key, files[key]);
                }
            });

            const response = await fetch('http://localhost:5000/verify-voter-step', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        }

        function displayServerResults(selfieResult, idResult, videoResult) {
            const analysisContent = document.getElementById('analysisContent');

            analysisContent.innerHTML = `
                <div class="data-extraction">
                    <h3>🔍 ID Data Extraction Results</h3>
                    <div class="extracted-data">
                        ${displayExtractedData(idResult)}
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>👥 Photo Comparison Results</h3>
                    <div class="comparison-grid">
                        ${displayServerComparison(videoResult)}
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>📊 Verification Summary</h3>
                    ${displayVerificationSummary(videoResult)}
                </div>

                <div class="analysis-section">
                    <h3>🔍 Step-by-Step Results</h3>
                    <div class="success">
                        <h4>Step 1 - Selfie Verification:</h4>
                        <strong>Status:</strong> ${selfieResult.success ? '✅ Success' : '❌ Failed'}<br>
                        <strong>Message:</strong> ${selfieResult.message}
                    </div>
                    <div class="success">
                        <h4>Step 2 - ID Verification:</h4>
                        <strong>Status:</strong> ${idResult.success ? '✅ Success' : '❌ Failed'}<br>
                        <strong>Message:</strong> ${idResult.message}<br>
                        <strong>Face Similarity:</strong> ${idResult.similarityPercentage || 'N/A'}%<br>
                        <strong>ID Scan Confidence:</strong> ${idResult.idScanConfidence || 'N/A'}%
                    </div>
                    <div class="success">
                        <h4>Step 3 - Complete Verification:</h4>
                        <strong>Status:</strong> ${videoResult.success ? '✅ Success' : '❌ Failed'}<br>
                        <strong>Message:</strong> ${videoResult.message}<br>
                        <strong>Overall Score:</strong> ${videoResult.overallScore || 'N/A'}%<br>
                        <strong>Verification ID:</strong> ${videoResult.verificationId || 'N/A'}
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>📋 Raw Server Response</h3>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 400px;">
${JSON.stringify({ selfieResult, idResult, videoResult }, null, 2)}
                    </pre>
                </div>
            `;
        }

        function displayExtractedData(idResult) {
            if (!idResult.success || !idResult.extractedData) {
                return '<div class="data-field">❌ Failed to extract data from ID</div>';
            }

            const data = idResult.extractedData;
            return `
                <div class="data-field"><strong>Name:</strong> ${data.name || 'Not found'}</div>
                <div class="data-field"><strong>ID Type:</strong> ${data.idType || 'Unknown'}</div>
                <div class="data-field"><strong>ID Number:</strong> ${data.idNumber || 'Not found'}</div>
                <div class="data-field"><strong>Date of Birth:</strong> ${data.dateOfBirth || 'Not found'}</div>
                <div class="data-field"><strong>Gender:</strong> ${data.gender || 'Not found'}</div>
                <div class="data-field"><strong>Address:</strong> ${data.address || 'Not found'}</div>
                <div class="data-field"><strong>Extraction Confidence:</strong> ${idResult.idScanConfidence || 'N/A'}%</div>
                <div class="data-field"><strong>Processing Time:</strong> ${idResult.processingTime || 'N/A'}ms</div>
            `;
        }

        function displayServerComparison(videoResult) {
            if (!videoResult.success || !videoResult.faceComparison) {
                return '<div class="comparison-item">❌ Face comparison not available</div>';
            }

            const comparisons = videoResult.faceComparison.comparisons;
            return `
                <div class="comparison-item">
                    <h4>Profile ↔ ID</h4>
                    <div class="similarity-score ${getScoreClass(comparisons.selfieToId.similarityPercentage)}">${comparisons.selfieToId.similarityPercentage}%</div>
                    <div>${comparisons.selfieToId.isMatch ? '✅ Match' : '❌ No Match'}</div>
                </div>
                <div class="comparison-item">
                    <h4>Profile ↔ Live</h4>
                    <div class="similarity-score ${getScoreClass(comparisons.selfieToVideo.similarityPercentage)}">${comparisons.selfieToVideo.similarityPercentage}%</div>
                    <div>${comparisons.selfieToVideo.isMatch ? '✅ Match' : '❌ No Match'}</div>
                </div>
                <div class="comparison-item">
                    <h4>ID ↔ Live</h4>
                    <div class="similarity-score ${getScoreClass(comparisons.idToVideo.similarityPercentage)}">${comparisons.idToVideo.similarityPercentage}%</div>
                    <div>${comparisons.idToVideo.isMatch ? '✅ Match' : '❌ No Match'}</div>
                </div>
            `;
        }

        function displayVerificationSummary(videoResult) {
            if (!videoResult.success) {
                return '<div class="error">❌ Verification failed</div>';
            }

            const overallScore = videoResult.overallScore || 0;
            const isVerified = overallScore >= 70;

            return `
                <div class="${isVerified ? 'success' : 'error'}">
                    <h4>${isVerified ? '✅ VERIFICATION SUCCESSFUL' : '❌ VERIFICATION FAILED'}</h4>
                    <strong>Overall Score:</strong> ${overallScore}% (Minimum required: 70%)<br>
                    <strong>Profile Match:</strong> ${videoResult.profileMatch?.overallMatch ? 'YES' : 'NO'} (${videoResult.profileMatch?.matchPercentage || 0}%)<br>
                    <strong>Face Match:</strong> ${videoResult.faceComparison?.isMatch ? 'YES' : 'NO'} (${videoResult.faceComparison?.weightedSimilarity || 0}%)<br>
                    <strong>Status:</strong> ${isVerified ? 'ELIGIBLE TO VOTE' : 'VERIFICATION REQUIRED'}<br>
                    <strong>Verification ID:</strong> ${videoResult.verificationId || 'N/A'}
                </div>
            `;
        }

        function getScoreClass(score) {
            if (score >= 80) return 'score-excellent';
            if (score >= 70) return 'score-good';
            if (score >= 60) return 'score-fair';
            return 'score-poor';
        }

        // Quick test functions
        function testServerVerification() {
            const serverResults = document.getElementById('serverResults');
            serverResults.innerHTML = '<div class="loading">🔄 Testing server connection...</div>';

            fetch('http://localhost:5000/health')
                .then(response => response.json())
                .then(data => {
                    serverResults.innerHTML = `
                        <div class="success">
                            ✅ Server is running and ready!<br>
                            <strong>Status:</strong> ${data.status}<br>
                            <strong>Features:</strong> Enhanced ID scanning, Photo comparison, Data extraction
                        </div>
                    `;
                })
                .catch(error => {
                    serverResults.innerHTML = `
                        <div class="error">
                            ❌ Server connection failed: ${error.message}<br>
                            Make sure the server is running on port 5000
                        </div>
                    `;
                });
        }

        function openComprehensiveTest() {
            window.open('file:///e:/Online-Voting-System-main/test-comprehensive-verification.html', '_blank');
        }

        function openMainApp() {
            window.open('http://localhost:3000', '_blank');
        }

        // Auto-test server on page load
        window.addEventListener('load', testServerVerification);
    </script>
</body>
</html>
