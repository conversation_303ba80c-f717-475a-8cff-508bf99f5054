<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Step-by-Step Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .step {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .step.active {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .step.completed {
            border-color: #28a745;
            background-color: #d4edda;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        button.success {
            background-color: #28a745;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Step-by-Step Verification Test</h1>
    
    <div class="progress">
        <div class="progress-bar" id="progressBar" style="width: 25%"></div>
    </div>
    <p id="progressText">Step 1 of 4</p>

    <div class="step active" id="step1">
        <h3>Step 1: Selfie Verification</h3>
        <p>Upload a selfie image for face detection.</p>
        <input type="file" id="selfieInput" accept="image/*">
        <button id="verifySelfie" onclick="verifySelfie()" disabled>Verify Selfie</button>
        <div id="selfieResult"></div>
    </div>

    <div class="step" id="step2">
        <h3>Step 2: ID Verification</h3>
        <p>Upload your government ID document.</p>
        <input type="file" id="idInput" accept="image/*">
        <button id="verifyId" onclick="verifyId()" disabled>Verify ID</button>
        <div id="idResult"></div>
    </div>

    <div class="step" id="step3">
        <h3>Step 3: Video Verification</h3>
        <p>Upload a video frame for live verification.</p>
        <input type="file" id="videoInput" accept="image/*">
        <button id="verifyVideo" onclick="verifyVideo()" disabled>Complete Verification</button>
        <div id="videoResult"></div>
    </div>

    <div class="step" id="step4">
        <h3>Step 4: Verification Complete</h3>
        <p>All verification steps completed successfully!</p>
        <div id="finalResult"></div>
    </div>

    <script>
        let currentStep = 1;
        let selfieFile = null;
        let idFile = null;
        let videoFile = null;

        // Enable file inputs
        document.getElementById('selfieInput').addEventListener('change', function(e) {
            selfieFile = e.target.files[0];
            document.getElementById('verifySelfie').disabled = !selfieFile;
        });

        document.getElementById('idInput').addEventListener('change', function(e) {
            idFile = e.target.files[0];
            document.getElementById('verifyId').disabled = !idFile;
        });

        document.getElementById('videoInput').addEventListener('change', function(e) {
            videoFile = e.target.files[0];
            document.getElementById('verifyVideo').disabled = !videoFile;
        });

        function updateProgress(step) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const percentage = (step / 4) * 100;
            
            progressBar.style.width = percentage + '%';
            progressText.textContent = `Step ${step} of 4`;
        }

        function moveToNextStep() {
            // Mark current step as completed
            document.getElementById(`step${currentStep}`).classList.remove('active');
            document.getElementById(`step${currentStep}`).classList.add('completed');
            
            currentStep++;
            updateProgress(currentStep);
            
            if (currentStep <= 4) {
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
        }

        function showResult(elementId, message, isSuccess) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }

        async function verifySelfie() {
            if (!selfieFile) return;

            const formData = new FormData();
            formData.append('selfie', selfieFile);
            formData.append('step', 'selfie');

            try {
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('selfieResult', '✓ Selfie verification successful!', true);
                    document.getElementById('verifySelfie').textContent = 'Completed';
                    document.getElementById('verifySelfie').classList.add('success');
                    document.getElementById('verifySelfie').disabled = true;
                    moveToNextStep();
                } else {
                    showResult('selfieResult', `✗ ${result.message}`, false);
                }
            } catch (error) {
                showResult('selfieResult', `✗ Error: ${error.message}`, false);
            }
        }

        async function verifyId() {
            if (!idFile || !selfieFile) return;

            const formData = new FormData();
            formData.append('idImage', idFile);
            formData.append('selfie', selfieFile);
            formData.append('step', 'id');

            try {
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('idResult', `✓ ID verification successful! Similarity: ${result.similarityPercentage}%`, true);
                    document.getElementById('verifyId').textContent = 'Completed';
                    document.getElementById('verifyId').classList.add('success');
                    document.getElementById('verifyId').disabled = true;
                    moveToNextStep();
                } else {
                    showResult('idResult', `✗ ${result.message}`, false);
                }
            } catch (error) {
                showResult('idResult', `✗ Error: ${error.message}`, false);
            }
        }

        async function verifyVideo() {
            if (!videoFile || !selfieFile || !idFile) return;

            const formData = new FormData();
            formData.append('videoFrame', videoFile);
            formData.append('selfie', selfieFile);
            formData.append('idImage', idFile);
            formData.append('step', 'video');

            try {
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('videoResult', `✓ Video verification successful! Similarity: ${result.similarityPercentage}%`, true);
                    document.getElementById('verifyVideo').textContent = 'Completed';
                    document.getElementById('verifyVideo').classList.add('success');
                    document.getElementById('verifyVideo').disabled = true;
                    moveToNextStep();
                    
                    // Show final result
                    showResult('finalResult', `🎉 All verification steps completed! Overall similarity: ${result.similarityPercentage}%<br>Verification ID: ${result.verificationId}`, true);
                } else {
                    showResult('videoResult', `✗ ${result.message}`, false);
                }
            } catch (error) {
                showResult('videoResult', `✗ Error: ${error.message}`, false);
            }
        }
    </script>
</body>
</html>
