<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive ID Verification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .step {
            border: 2px solid #ddd;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .step.active {
            border-color: #007bff;
            background-color: #f8f9fa;
            box-shadow: 0 2px 8px rgba(0,123,255,0.2);
        }
        .step.completed {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .step.failed {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #6c757d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .step.active .step-number {
            background-color: #007bff;
        }
        .step.completed .step-number {
            background-color: #28a745;
        }
        .step.failed .step-number {
            background-color: #dc3545;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .success {
            background-color: #28a745;
        }
        .success:hover {
            background-color: #218838;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
        }
        .result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .data-display {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .verification-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .verification-summary.success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        .verification-summary.failed {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        }
        .file-input {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .comparison-item {
            text-align: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .similarity-score {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .match-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 5px;
        }
        .match-status.match {
            background-color: #d4edda;
            color: #155724;
        }
        .match-status.no-match {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Comprehensive ID Verification System</h1>
        <p>This system demonstrates advanced ID scanning with data extraction and multi-level photo comparison.</p>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 25%"></div>
        </div>
        <p id="progressText">Step 1 of 4 - Upload Selfie</p>

        <div class="step active" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <h3>Profile Photo Upload</h3>
            </div>
            <p>Upload your profile selfie for face detection and verification.</p>
            <input type="file" id="selfieInput" class="file-input" accept="image/*">
            <button id="verifySelfie" onclick="verifySelfie()" disabled>Verify Selfie</button>
            <div id="selfieResult"></div>
        </div>

        <div class="step" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <h3>ID Document Scanning</h3>
            </div>
            <p>Upload your government ID for data extraction and face comparison.</p>
            <input type="file" id="idInput" class="file-input" accept="image/*">
            <button id="verifyId" onclick="verifyId()" disabled>Scan & Verify ID</button>
            <div id="idResult"></div>
            <div id="extractedData" class="data-display" style="display: none;"></div>
        </div>

        <div class="step" id="step3">
            <div class="step-header">
                <div class="step-number">3</div>
                <h3>Live Video Verification</h3>
            </div>
            <p>Upload a live video frame for comprehensive verification.</p>
            <input type="file" id="videoInput" class="file-input" accept="image/*">
            <button id="verifyVideo" onclick="verifyVideo()" disabled>Complete Verification</button>
            <div id="videoResult"></div>
            <div id="comprehensiveResults" style="display: none;">
                <h4>📊 Comprehensive Analysis Results</h4>
                <div class="comparison-grid" id="comparisonGrid"></div>
                <div id="profileMatchResults" class="data-display"></div>
            </div>
        </div>

        <div class="step" id="step4">
            <div class="step-header">
                <div class="step-number">4</div>
                <h3>Verification Complete</h3>
            </div>
            <div id="finalResult"></div>
            <div id="verificationSummary" class="verification-summary" style="display: none;">
                <h3>🎉 Verification Status</h3>
                <div id="summaryContent"></div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let selfieFile = null;
        let idFile = null;
        let videoFile = null;
        let extractedIDData = null;
        let verificationResults = {};

        // File input handlers
        document.getElementById('selfieInput').addEventListener('change', function(e) {
            selfieFile = e.target.files[0];
            document.getElementById('verifySelfie').disabled = !selfieFile;
        });

        document.getElementById('idInput').addEventListener('change', function(e) {
            idFile = e.target.files[0];
            document.getElementById('verifyId').disabled = !idFile;
        });

        document.getElementById('videoInput').addEventListener('change', function(e) {
            videoFile = e.target.files[0];
            document.getElementById('verifyVideo').disabled = !videoFile;
        });

        function updateProgress(step) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const percentage = (step / 4) * 100;
            
            progressFill.style.width = percentage + '%';
            
            const stepNames = ['Upload Selfie', 'Scan ID Document', 'Live Verification', 'Complete'];
            progressText.textContent = `Step ${step} of 4 - ${stepNames[step - 1]}`;
        }

        function moveToNextStep(success = true) {
            const currentStepEl = document.getElementById(`step${currentStep}`);
            currentStepEl.classList.remove('active');
            currentStepEl.classList.add(success ? 'completed' : 'failed');
            
            if (success) {
                currentStep++;
                updateProgress(currentStep);
                
                if (currentStep <= 4) {
                    document.getElementById(`step${currentStep}`).classList.add('active');
                }
            }
        }

        function showResult(elementId, message, isSuccess, data = null) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${message}</div>`;
            
            if (data && isSuccess) {
                const dataDiv = document.createElement('div');
                dataDiv.className = 'data-display';
                dataDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.appendChild(dataDiv);
            }
        }

        async function verifySelfie() {
            if (!selfieFile) return;

            const formData = new FormData();
            formData.append('selfie', selfieFile);
            formData.append('step', 'selfie');

            try {
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('selfieResult', '✓ Selfie verification successful! Face detected.', true);
                    document.getElementById('verifySelfie').textContent = 'Completed';
                    document.getElementById('verifySelfie').classList.add('success');
                    document.getElementById('verifySelfie').disabled = true;
                    verificationResults.selfie = result;
                    moveToNextStep();
                } else {
                    showResult('selfieResult', `✗ ${result.message}`, false);
                    moveToNextStep(false);
                }
            } catch (error) {
                showResult('selfieResult', `✗ Error: ${error.message}`, false);
                moveToNextStep(false);
            }
        }

        async function verifyId() {
            if (!idFile || !selfieFile) return;

            const formData = new FormData();
            formData.append('idImage', idFile);
            formData.append('selfie', selfieFile);
            formData.append('step', 'id');

            try {
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    extractedIDData = result.extractedData;
                    verificationResults.id = result;
                    
                    showResult('idResult', 
                        `✓ ID verification successful!<br>
                         📋 Data extracted from ${result.extractedData.idType}<br>
                         👤 Name: ${result.extractedData.name}<br>
                         🎯 Face similarity: ${result.similarityPercentage}%<br>
                         📊 Scan confidence: ${result.idScanConfidence}%`, 
                        true
                    );
                    
                    // Show extracted data
                    const extractedDataDiv = document.getElementById('extractedData');
                    extractedDataDiv.style.display = 'block';
                    extractedDataDiv.textContent = JSON.stringify(result.extractedData, null, 2);
                    
                    document.getElementById('verifyId').textContent = 'Completed';
                    document.getElementById('verifyId').classList.add('success');
                    document.getElementById('verifyId').disabled = true;
                    moveToNextStep();
                } else {
                    showResult('idResult', `✗ ${result.message}`, false, result);
                    moveToNextStep(false);
                }
            } catch (error) {
                showResult('idResult', `✗ Error: ${error.message}`, false);
                moveToNextStep(false);
            }
        }

        async function verifyVideo() {
            if (!videoFile || !selfieFile || !idFile) return;

            const formData = new FormData();
            formData.append('videoFrame', videoFile);
            formData.append('selfie', selfieFile);
            formData.append('idImage', idFile);
            formData.append('step', 'video');

            try {
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    verificationResults.video = result;
                    
                    showResult('videoResult', 
                        `✓ Comprehensive verification successful!<br>
                         🎯 Overall Score: ${result.overallScore}%<br>
                         📊 Face Similarity: ${result.similarityPercentage}%<br>
                         🆔 Verification ID: ${result.verificationId}`, 
                        true
                    );
                    
                    // Show comprehensive results
                    showComprehensiveResults(result);
                    
                    document.getElementById('verifyVideo').textContent = 'Completed';
                    document.getElementById('verifyVideo').classList.add('success');
                    document.getElementById('verifyVideo').disabled = true;
                    moveToNextStep();
                    
                    // Show final verification summary
                    showVerificationSummary(result);
                } else {
                    showResult('videoResult', `✗ ${result.message}`, false, result);
                    moveToNextStep(false);
                }
            } catch (error) {
                showResult('videoResult', `✗ Error: ${error.message}`, false);
                moveToNextStep(false);
            }
        }

        function showComprehensiveResults(result) {
            const comprehensiveDiv = document.getElementById('comprehensiveResults');
            comprehensiveDiv.style.display = 'block';
            
            // Show face comparison grid
            const comparisonGrid = document.getElementById('comparisonGrid');
            const comparisons = result.faceComparison.comparisons;
            
            comparisonGrid.innerHTML = `
                <div class="comparison-item">
                    <div>Profile ↔ ID</div>
                    <div class="similarity-score">${comparisons.selfieToId.similarityPercentage}%</div>
                    <div class="match-status ${comparisons.selfieToId.isMatch ? 'match' : 'no-match'}">
                        ${comparisons.selfieToId.isMatch ? 'MATCH' : 'NO MATCH'}
                    </div>
                </div>
                <div class="comparison-item">
                    <div>Profile ↔ Live</div>
                    <div class="similarity-score">${comparisons.selfieToVideo.similarityPercentage}%</div>
                    <div class="match-status ${comparisons.selfieToVideo.isMatch ? 'match' : 'no-match'}">
                        ${comparisons.selfieToVideo.isMatch ? 'MATCH' : 'NO MATCH'}
                    </div>
                </div>
                <div class="comparison-item">
                    <div>ID ↔ Live</div>
                    <div class="similarity-score">${comparisons.idToVideo.similarityPercentage}%</div>
                    <div class="match-status ${comparisons.idToVideo.isMatch ? 'match' : 'no-match'}">
                        ${comparisons.idToVideo.isMatch ? 'MATCH' : 'NO MATCH'}
                    </div>
                </div>
            `;
            
            // Show profile match results
            const profileMatchDiv = document.getElementById('profileMatchResults');
            profileMatchDiv.textContent = `Profile Data Matching Results:\n${JSON.stringify(result.profileMatch, null, 2)}`;
        }

        function showVerificationSummary(result) {
            const summaryDiv = document.getElementById('verificationSummary');
            const summaryContent = document.getElementById('summaryContent');
            
            summaryDiv.style.display = 'block';
            summaryDiv.className = `verification-summary ${result.overallScore >= 70 ? 'success' : 'failed'}`;
            
            summaryContent.innerHTML = `
                <h2>${result.overallScore >= 70 ? '✅ VERIFIED' : '❌ VERIFICATION FAILED'}</h2>
                <p><strong>Overall Score:</strong> ${result.overallScore}%</p>
                <p><strong>Verification ID:</strong> ${result.verificationId}</p>
                <p><strong>Profile Match:</strong> ${result.profileMatch.overallMatch ? 'YES' : 'NO'} (${result.profileMatch.matchPercentage}%)</p>
                <p><strong>Face Match:</strong> ${result.faceComparison.isMatch ? 'YES' : 'NO'} (${result.faceComparison.weightedSimilarity}%)</p>
                <p><strong>Status:</strong> ${result.overallScore >= 70 ? 'ELIGIBLE TO VOTE' : 'VERIFICATION REQUIRED'}</p>
            `;
        }

        // Initialize
        updateProgress(1);
    </script>
</body>
</html>
