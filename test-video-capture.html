<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Video Capture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .video-container {
            position: relative;
            display: inline-block;
            margin: 20px 0;
        }
        video {
            width: 320px;
            height: 240px;
            border: 2px solid #ddd;
            border-radius: 8px;
            transform: scaleX(-1); /* Mirror the video */
        }
        canvas {
            display: none;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .success {
            background-color: #28a745;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success-msg {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .captured-image {
            max-width: 320px;
            border: 2px solid #28a745;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Video Capture Test</h1>
    <p>This page tests the simplified video capture functionality.</p>

    <div class="video-container">
        <video id="video" autoplay muted playsinline></video>
        <canvas id="canvas"></canvas>
    </div>

    <div>
        <button id="startBtn" onclick="startCamera()">Start Camera</button>
        <button id="captureBtn" onclick="captureFrame()" disabled>Capture Frame</button>
        <button id="stopBtn" onclick="stopCamera()" disabled>Stop Camera</button>
    </div>

    <div id="status"></div>
    <div id="capturedImage"></div>

    <script>
        let video = document.getElementById('video');
        let canvas = document.getElementById('canvas');
        let stream = null;
        let retryAttempts = 0;

        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${isError ? 'error' : 'success-msg'}">${message}</div>`;
        }

        async function startCamera() {
            try {
                showStatus('Requesting camera access...');
                
                // Simple constraints - start with basic and work up
                let constraints;
                if (retryAttempts > 0) {
                    constraints = {
                        video: {
                            width: { ideal: 320 },
                            height: { ideal: 240 }
                        },
                        audio: false
                    };
                    console.log('Using basic constraints for retry');
                } else {
                    constraints = {
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: 'user'
                        },
                        audio: false
                    };
                }

                stream = await navigator.mediaDevices.getUserMedia(constraints);
                console.log('Camera access granted:', stream);

                // Simple video setup
                video.srcObject = stream;
                video.autoplay = true;
                video.playsInline = true;
                video.muted = true;
                
                // Wait for video to be ready
                await new Promise((resolve, reject) => {
                    video.onloadedmetadata = () => {
                        video.play()
                            .then(resolve)
                            .catch(reject);
                    };
                    video.onerror = reject;
                });

                // Update UI
                document.getElementById('startBtn').disabled = true;
                document.getElementById('captureBtn').disabled = false;
                document.getElementById('stopBtn').disabled = false;
                
                showStatus('✓ Camera started successfully!');
                retryAttempts = 0; // Reset retry counter on success

            } catch (error) {
                console.error('Error accessing camera:', error);
                
                // Simple error handling
                let errorMessage = 'Failed to access camera. ';
                
                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Please allow camera access and try again.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found. Please connect a camera and try again.';
                } else if (error.name === 'OverconstrainedError' && retryAttempts < 2) {
                    console.log('Retrying with basic constraints...');
                    retryAttempts++;
                    setTimeout(() => startCamera(), 1000);
                    return;
                } else {
                    errorMessage += 'Please check your camera and try again.';
                }
                
                showStatus(errorMessage, true);
            }
        }

        function captureFrame() {
            if (!video || !canvas) {
                showStatus('Video or canvas not available', true);
                return;
            }

            try {
                console.log('Attempting to capture video frame...');

                // Check if video is playing and has dimensions
                if (video.videoWidth === 0 || video.videoHeight === 0) {
                    showStatus('Cannot capture frame: video not ready. Please wait a moment and try again.', true);
                    return;
                }

                console.log(`Video dimensions: ${video.videoWidth}x${video.videoHeight}`);

                // Set canvas dimensions to match video
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;

                // Draw the current video frame to the canvas
                const context = canvas.getContext('2d');

                // Clear the canvas first
                context.clearRect(0, 0, canvas.width, canvas.height);

                // Mirror the image horizontally to match the mirrored video display
                context.translate(canvas.width, 0);
                context.scale(-1, 1);
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Reset transformation matrix to default
                context.setTransform(1, 0, 0, 1, 0, 0);

                // Convert canvas to blob with higher quality
                canvas.toBlob((blob) => {
                    if (blob) {
                        console.log(`Captured frame as blob: ${blob.size} bytes`);

                        // Create a File object from the blob
                        const file = new File([blob], 'video-frame.jpg', { type: 'image/jpeg' });
                        
                        // Show the captured image
                        const url = URL.createObjectURL(blob);
                        document.getElementById('capturedImage').innerHTML = 
                            `<div class="success-msg">✓ Frame captured successfully! Size: ${blob.size} bytes</div>
                             <img src="${url}" alt="Captured frame" class="captured-image">`;
                        
                        showStatus('✓ Frame captured successfully!');
                        
                        // Stop the camera after successful capture
                        stopCamera();
                    } else {
                        console.error('Failed to create blob from canvas');
                        showStatus('Failed to capture video frame. Please try again.', true);
                    }
                }, 'image/jpeg', 0.95); // 95% quality
            } catch (error) {
                console.error('Error capturing video frame:', error);
                showStatus(`Error capturing video frame: ${error.message}`, true);
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => {
                    track.stop();
                    console.log('Stopped track:', track.kind);
                });
                stream = null;
            }

            video.srcObject = null;

            // Update UI
            document.getElementById('startBtn').disabled = false;
            document.getElementById('captureBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;
            
            showStatus('Camera stopped.');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            stopCamera();
        });
    </script>
</body>
</html>
