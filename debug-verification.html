<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Verification Process</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .step {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .step.active {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .step.completed {
            border-color: #28a745;
            background-color: #d4edda;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .success {
            background-color: #28a745;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success-msg {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .debug-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .verification-status {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .verified {
            background-color: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        .not-verified {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
    </style>
</head>
<body>
    <h1>Debug Verification Process</h1>
    <p>This page helps debug the step-by-step verification process and identify where it might be failing.</p>

    <div class="verification-status" id="verificationStatus">
        <div class="not-verified">❌ Not Verified - Cannot Vote</div>
    </div>

    <div class="step active" id="step1">
        <h3>Step 1: Selfie Verification</h3>
        <input type="file" id="selfieInput" accept="image/*">
        <button id="verifySelfie" onclick="verifySelfie()" disabled>Verify Selfie</button>
        <div id="selfieResult"></div>
        <div id="selfieDebug" class="debug-info"></div>
    </div>

    <div class="step" id="step2">
        <h3>Step 2: ID Verification</h3>
        <input type="file" id="idInput" accept="image/*">
        <button id="verifyId" onclick="verifyId()" disabled>Verify ID</button>
        <div id="idResult"></div>
        <div id="idDebug" class="debug-info"></div>
    </div>

    <div class="step" id="step3">
        <h3>Step 3: Video Verification</h3>
        <input type="file" id="videoInput" accept="image/*">
        <button id="verifyVideo" onclick="verifyVideo()" disabled>Complete Verification</button>
        <div id="videoResult"></div>
        <div id="videoDebug" class="debug-info"></div>
    </div>

    <div class="step" id="step4">
        <h3>Step 4: Verification Complete</h3>
        <div id="finalResult"></div>
        <div id="finalDebug" class="debug-info"></div>
    </div>

    <script>
        let currentStep = 1;
        let selfieFile = null;
        let idFile = null;
        let videoFile = null;
        let verificationId = null;
        let isVerified = false;

        // Enable file inputs
        document.getElementById('selfieInput').addEventListener('change', function(e) {
            selfieFile = e.target.files[0];
            document.getElementById('verifySelfie').disabled = !selfieFile;
            updateDebug('selfieDebug', `Selfie file selected: ${selfieFile ? selfieFile.name : 'None'}`);
        });

        document.getElementById('idInput').addEventListener('change', function(e) {
            idFile = e.target.files[0];
            document.getElementById('verifyId').disabled = !idFile;
            updateDebug('idDebug', `ID file selected: ${idFile ? idFile.name : 'None'}`);
        });

        document.getElementById('videoInput').addEventListener('change', function(e) {
            videoFile = e.target.files[0];
            document.getElementById('verifyVideo').disabled = !videoFile;
            updateDebug('videoDebug', `Video file selected: ${videoFile ? videoFile.name : 'None'}`);
        });

        function updateDebug(elementId, message) {
            const debugDiv = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.textContent += `[${timestamp}] ${message}\n`;
        }

        function showResult(elementId, message, isSuccess) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.innerHTML = `<div class="${isSuccess ? 'success-msg' : 'error'}">${message}</div>`;
        }

        function moveToNextStep() {
            // Mark current step as completed
            document.getElementById(`step${currentStep}`).classList.remove('active');
            document.getElementById(`step${currentStep}`).classList.add('completed');
            
            currentStep++;
            
            if (currentStep <= 4) {
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
        }

        function updateVerificationStatus() {
            const statusDiv = document.getElementById('verificationStatus');
            if (isVerified && verificationId) {
                statusDiv.innerHTML = `<div class="verified">✅ Verified - Can Vote (ID: ${verificationId})</div>`;
            } else {
                statusDiv.innerHTML = `<div class="not-verified">❌ Not Verified - Cannot Vote</div>`;
            }
        }

        async function verifySelfie() {
            if (!selfieFile) return;

            updateDebug('selfieDebug', 'Starting selfie verification...');

            const formData = new FormData();
            formData.append('selfie', selfieFile);
            formData.append('step', 'selfie');

            try {
                updateDebug('selfieDebug', 'Sending request to server...');
                
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                updateDebug('selfieDebug', `Response status: ${response.status}`);
                
                const result = await response.json();
                updateDebug('selfieDebug', `Response data: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    showResult('selfieResult', '✓ Selfie verification successful!', true);
                    document.getElementById('verifySelfie').textContent = 'Completed';
                    document.getElementById('verifySelfie').classList.add('success');
                    document.getElementById('verifySelfie').disabled = true;
                    moveToNextStep();
                    updateDebug('selfieDebug', 'Selfie verification completed successfully');
                } else {
                    showResult('selfieResult', `✗ ${result.message}`, false);
                    updateDebug('selfieDebug', `Selfie verification failed: ${result.message}`);
                }
            } catch (error) {
                showResult('selfieResult', `✗ Error: ${error.message}`, false);
                updateDebug('selfieDebug', `Error: ${error.message}\nStack: ${error.stack}`);
            }
        }

        async function verifyId() {
            if (!idFile || !selfieFile) return;

            updateDebug('idDebug', 'Starting ID verification...');

            const formData = new FormData();
            formData.append('idImage', idFile);
            formData.append('selfie', selfieFile);
            formData.append('step', 'id');

            try {
                updateDebug('idDebug', 'Sending request to server...');
                
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                updateDebug('idDebug', `Response status: ${response.status}`);
                
                const result = await response.json();
                updateDebug('idDebug', `Response data: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    showResult('idResult', `✓ ID verification successful! Similarity: ${result.similarityPercentage}%`, true);
                    document.getElementById('verifyId').textContent = 'Completed';
                    document.getElementById('verifyId').classList.add('success');
                    document.getElementById('verifyId').disabled = true;
                    moveToNextStep();
                    updateDebug('idDebug', 'ID verification completed successfully');
                } else {
                    showResult('idResult', `✗ ${result.message}`, false);
                    updateDebug('idDebug', `ID verification failed: ${result.message}`);
                }
            } catch (error) {
                showResult('idResult', `✗ Error: ${error.message}`, false);
                updateDebug('idDebug', `Error: ${error.message}\nStack: ${error.stack}`);
            }
        }

        async function verifyVideo() {
            if (!videoFile || !selfieFile || !idFile) return;

            updateDebug('videoDebug', 'Starting video verification...');

            const formData = new FormData();
            formData.append('videoFrame', videoFile);
            formData.append('selfie', selfieFile);
            formData.append('idImage', idFile);
            formData.append('step', 'video');

            try {
                updateDebug('videoDebug', 'Sending request to server...');
                
                const response = await fetch('http://localhost:5000/verify-voter-step', {
                    method: 'POST',
                    body: formData
                });

                updateDebug('videoDebug', `Response status: ${response.status}`);
                
                const result = await response.json();
                updateDebug('videoDebug', `Response data: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    // This is the critical part - check if verificationId is returned
                    verificationId = result.verificationId;
                    isVerified = !!verificationId;
                    
                    updateDebug('videoDebug', `Verification ID received: ${verificationId}`);
                    updateDebug('videoDebug', `Is verified: ${isVerified}`);
                    
                    showResult('videoResult', `✓ Video verification successful! Similarity: ${result.similarityPercentage}%`, true);
                    document.getElementById('verifyVideo').textContent = 'Completed';
                    document.getElementById('verifyVideo').classList.add('success');
                    document.getElementById('verifyVideo').disabled = true;
                    moveToNextStep();
                    
                    // Show final result
                    showResult('finalResult', `🎉 All verification steps completed!<br>Verification ID: ${verificationId}<br>Overall similarity: ${result.similarityPercentage}%`, true);
                    updateDebug('finalDebug', `Complete verification successful with ID: ${verificationId}`);
                    
                    // Update verification status
                    updateVerificationStatus();
                    
                    // Simulate what should happen in the main app
                    updateDebug('finalDebug', 'In the main app, this should now enable voting...');
                    updateDebug('finalDebug', `handleVerificationComplete(${verificationId}) should be called`);
                    updateDebug('finalDebug', `isVerified should be set to: ${isVerified}`);
                    updateDebug('finalDebug', `showVerification should be set to: false`);
                    
                } else {
                    showResult('videoResult', `✗ ${result.message}`, false);
                    updateDebug('videoDebug', `Video verification failed: ${result.message}`);
                }
            } catch (error) {
                showResult('videoResult', `✗ Error: ${error.message}`, false);
                updateDebug('videoDebug', `Error: ${error.message}\nStack: ${error.stack}`);
            }
        }

        // Initialize
        updateVerificationStatus();
    </script>
</body>
</html>
